# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Page Afore
previous_label=Previous
next.title=Page Efter
next_label=Neist

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Page
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=o {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} o {{pagesCount}})

zoom_out.title=Zoom Oot
zoom_out_label=Zoom Oot
zoom_in.title=Zoom In
zoom_in_label=Zoom In
zoom.title=Zoom
presentation_mode.title=Flit tae Presentation Mode
presentation_mode_label=Presentation Mode
open_file.title=Open File
open_file_label=Open
print.title=Prent
print_label=Prent

# Secondary toolbar and context menu
tools.title=Tools
tools_label=Tools
first_page.title=Gang tae First Page
first_page_label=Gang tae First Page
last_page.title=Gang tae Lest Page
last_page_label=Gang tae Lest Page
page_rotate_cw.title=Rotate Clockwise
page_rotate_cw_label=Rotate Clockwise
page_rotate_ccw.title=Rotate Coonterclockwise
page_rotate_ccw_label=Rotate Coonterclockwise

cursor_text_select_tool.title=Enable Text Walin Tool
cursor_text_select_tool_label=Text Walin Tool
cursor_hand_tool.title=Enable Haun Tool
cursor_hand_tool_label=Haun Tool

scroll_vertical.title=Yaise Vertical Scrollin
scroll_vertical_label=Vertical Scrollin
scroll_horizontal.title=Yaise Horizontal Scrollin
scroll_horizontal_label=Horizontal Scrollin
scroll_wrapped.title=Yaise Wrapped Scrollin
scroll_wrapped_label=Wrapped Scrollin

spread_none.title=Dinnae jyn page spreids
spread_none_label=Nae Spreids
spread_odd.title=Jyn page spreids stertin wi odd-numbered pages
spread_odd_label=Odd Spreids
spread_even.title=Jyn page spreids stertin wi even-numbered pages
spread_even_label=Even Spreids

# Document properties dialog box
document_properties.title=Document Properties…
document_properties_label=Document Properties…
document_properties_file_name=File nemme:
document_properties_file_size=File size:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Title:
document_properties_author=Author:
document_properties_subject=Subjeck:
document_properties_keywords=Keywirds:
document_properties_creation_date=Date o Makkin:
document_properties_modification_date=Date o Chynges:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Makker:
document_properties_producer=PDF Producer:
document_properties_version=PDF Version:
document_properties_page_count=Page Coont:
document_properties_page_size=Page Size:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=portrait
document_properties_page_size_orientation_landscape=landscape
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Fast Wab View:
document_properties_linearized_yes=Aye
document_properties_linearized_no=Naw
document_properties_close=Sneck

print_progress_message=Reddin document fur prentin…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Stap

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Toggle Sidebaur
toggle_sidebar_notification2.title=Toggle Sidebaur (document conteens ootline/attachments/layers)
toggle_sidebar_label=Toggle Sidebaur
document_outline.title=Kythe Document Ootline (double-click fur tae oot-fauld/in-fauld aw items)
document_outline_label=Document Ootline
attachments.title=Kythe Attachments
attachments_label=Attachments
layers.title=Kythe Layers (double-click fur tae reset aw layers tae the staunart state)
layers_label=Layers
thumbs.title=Kythe Thumbnails
thumbs_label=Thumbnails
current_outline_item.title=Find Current Ootline Item
current_outline_item_label=Current Ootline Item
findbar.title=Find in Document
findbar_label=Find

additional_layers=Mair Layers
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Page {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Page {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Thumbnail o Page {{page}}

# Find panel button title and messages
find_input.title=Find
find_input.placeholder=Find in document…
find_previous.title=Airt oot the last time this phrase occurred
find_previous_label=Previous
find_next.title=Airt oot the neist time this phrase occurs
find_next_label=Neist
find_highlight=Highlicht aw
find_match_case_label=Match case
find_entire_word_label=Hale Wirds
find_reached_top=Raxed tap o document, went on fae the dowp end
find_reached_bottom=Raxed end o document, went on fae the tap
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} o {{total}} match
find_match_count[two]={{current}} o {{total}} matches
find_match_count[few]={{current}} o {{total}} matches
find_match_count[many]={{current}} o {{total}} matches
find_match_count[other]={{current}} o {{total}} matches
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Mair nor {{limit}} matches
find_match_count_limit[one]=Mair nor {{limit}} match
find_match_count_limit[two]=Mair nor {{limit}} matches
find_match_count_limit[few]=Mair nor {{limit}} matches
find_match_count_limit[many]=Mair nor {{limit}} matches
find_match_count_limit[other]=Mair nor {{limit}} matches
find_not_found=Phrase no fund

# Predefined zoom values
page_scale_width=Page Width
page_scale_fit=Page Fit
page_scale_auto=Automatic Zoom
page_scale_actual=Actual Size
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

loading_error=An mishanter tuik place while loadin the PDF.
invalid_file_error=No suithfest or camshauchlet PDF file.
missing_file_error=PDF file tint.
unexpected_response_error=Unexpectit server repone.

rendering_error=A mishanter tuik place while renderin the page.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Annotation]
password_label=Inpit the passwird fur tae open this PDF file.
password_invalid=Passwird no suithfest. Gonnae gie it anither shot.
password_ok=OK
password_cancel=Stap

printing_not_supported=Tak tent: Prentin isnae richt supportit by this stravaiger.
printing_not_ready=Tak tent: The PDF isnae richt loadit fur prentin.
web_fonts_disabled=Wab fonts are disabled: cannae yaise embeddit PDF fonts.

